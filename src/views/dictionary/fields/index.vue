<template>
  <n-spin :show="isLoading" description="数据加载中..." size="large">
    <n-space justify="center" :size="24" style="padding: 24px" class="card">
      <n-card 
        v-for="(item, key) in cardData" 
        :key="key"
        hoverable
        :title="`${key} 数据`"
        header-style="padding: 16px"
        content-style="padding: 20px"
        style="width: 300px; box-shadow: 0 2px 8px rgba(0,0,0,0.1)"
        @click="handleCardClick(key)"
      >
        <template #header-extra>
          <n-tag type="info" round>{{ key }}</n-tag>
        </template>

        <n-space vertical :size="12">
          <n-statistic label="车型总量" :value="item.count" />
          <n-statistic label="零件总量" :value="item.part" />
          <n-statistic label="VIO" :value="item.vio" />
        </n-space>
      </n-card>
    </n-space>
  </n-spin>
  <n-modal 
    v-model:show="showModal" 
    @after-leave="handleClose"
    preset="card"
    :title="`${currentKey} 车型`"
    style="width: 90%; max-width: 1200px;"
    :content-style="{  overflow: 'hidden' }"
  >
    <div v-if="currentKey === 'ACES' || currentKey === 'LY'">
      <div class="modal-container" style="height: 100%; display: flex; flex-direction: column; gap: 16px;">
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="车型数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addSource" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <n-space vertical style="margin-bottom: 12px;">
              <n-select
                filterable
                v-model:value="sourceValue"
                placeholder="车型表"
                :options="sourceOptions"
                clearable
                @update:value="sourceChange"
              />
            </n-space>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="sourceColumns"
                :data="sourceTable"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div> 
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="零件数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addPartACES" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="partColumnsACES"
                :data="partTableACES"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;max-height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div>
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="VIO" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addvioACES" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-space vertical style="margin-bottom: 12px;">
                <n-select
                  filterable
                  v-model:value="vioValueACES"
                  placeholder="VIO表"
                  :options="vioOptionsACES"
                  clearable
                  @update:value="vioChangeACES"
                />
              </n-space>
              <n-data-table
                :columns="vioColumnsACES"
                :data="vioTableACES"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;max-height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div>
        <n-button @click="Save" type="primary" class="mb-4">保存</n-button>
      </div>
    </div>
    <!-- <div v-else-if="currentKey === 'LY'">
      <div class="modal-container" style="height: 100%; display: flex; flex-direction: column; gap: 16px;">
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="车型数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addSource" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <n-space vertical style="margin-bottom: 12px;">
              <n-select
                filterable
                v-model:value="sourceValue"
                placeholder="车型表"
                :options="sourceOptions"
                clearable
                @update:value="sourceChange"
              />
            </n-space>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="sourceColumns"
                :data="sourceTable"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;height: 280px;"
                :max-height="230"
              />
            </div>
            
          </n-card>
        </div>
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="零件数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addPartLY" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <n-space vertical style="margin-bottom: 12px;">
              <n-select
                filterable
                v-model:value="partValue"
                placeholder="零件表"
                :options="partOptions"
                clearable
                @update:value="partChange"
              />
            </n-space>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="partColumnsLY"
                :data="partTableLY"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;max-height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div>
        <n-button @click="Save" type="primary" class="mb-4">保存</n-button>
      </div>
    </div> -->

    <div v-else-if="currentKey === '_007'">
      <div class="modal-container" style="height: 100%; display: flex; flex-direction: column; gap: 16px;">
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="车型数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addSource" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <n-space vertical style="margin-bottom: 12px;">
              <n-select
                filterable
                v-model:value="sourceValue"
                placeholder="车型表"
                :options="sourceOptions"
                clearable
                @update:value="sourceChange"
              />
            </n-space>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="sourceColumns"
                :data="sourceTable"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div> 
        <n-button @click="Save" type="primary" class="mb-4">保存</n-button>
      </div>
    </div>

    <div v-else-if="currentKey === 'TECDOC'">
      <div class="modal-container" style="height: 100%; display: flex; flex-direction: column; gap: 16px;">
        <div class="table-section" style="flex: 1; min-height: 200px; overflow: hidden;">          
          <n-card title="车型数据" class="table-card" content-style="height: 100%; display: flex; flex-direction: column;">
            <n-button @click="addSource" type="primary" class="mb-3" style="max-width: 100px;">新增行</n-button>
            <n-space vertical style="margin-bottom: 12px;">
              <n-select
                filterable
                v-model:value="sourceValue"
                placeholder="车型表"
                :options="sourceOptions"
                clearable
                @update:value="sourceChange"
              />
            </n-space>
            <div class="table-container" style="flex: 1; overflow: auto;">
              <n-data-table
                :columns="sourceColumns"
                :data="sourceTable"
                :bordered="false"
                :scroll-x="1000"
                style="min-width: 800px;height: 280px;"
                :max-height="230"
              />
            </div>
          </n-card>
        </div>
        <n-button @click="Save" type="primary" class="mb-4">保存</n-button>
      </div>
    </div>
  </n-modal>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import api from '@/api'
import {
  NCard,
  NSpace,
  NTag,
  NStatistic,
  NModal,
  NDataTable,
  NSelect,
  NInput,
  NButton
} from 'naive-ui'

// 新增加载状态
const isLoading = ref(true)
// 卡片数据
const cardData = ref({})
// 当前选中的卡片
const currentKey = ref('')
// 弹窗显示状态
const showModal = ref(false)

const sourceValue = ref([])
const sourceOptions = ref([])
const sourceTable = ref([])
const sourceselectOptions = ref([])
const sourceselectOptionsLY = ref([])
const partOptions = ref([])
const partTableACES = ref([])
const partTableLY = ref([])
const partselectOptionsACES = ref([])
const partValue = ref([])
const vioValueACES = ref([])
const vioOptionsACES = ref([])
const vioTableACES = ref([])
const vioselectOptionsACES = ref([])

const loadingModels = ref(false) // 下拉框加载状态

// ACES_VIO表格列配置
const vioColumnsACES = ref([
  {
    title: '关联字段',
    key: 'vio',
    render(row) {
        return h(NSelect, {
        options: sourceselectOptions.value,
        value: row.vioselectVal,
        filterable: true,
        clearable: true,
        consistentMenuWidth: false,
        // 优化虚拟滚动
        virtualScroll: true,
        // 添加样式穿透
        themeOverrides: {
          menuPadding: '4px'
        },
        onUpdateValue(v) {
          row.vioVal = v
        },
        onKeydown: (e) => {
          if (e.key === 'Enter') {
            e.preventDefault()
            const value = e.target.value.trim()
            if (value) {
              handleCreate(value, { row })
            }
          }
        }
      })
    },
  },
  {
    title: '源数据字段',
    key: 'vio',
    render(row) {
        return h(NSelect, {
        options: vioselectOptionsACES.value,
        value: row.sourceselectVal,
        filterable: true,
        clearable: true,
        consistentMenuWidth: false,
        // 优化虚拟滚动
        virtualScroll: true,
        // 添加样式穿透
        themeOverrides: {
          menuPadding: '4px'
        },
        onUpdateValue(v) {
          row.selectVal = v
        },
        onKeydown: (e) => {
          if (e.key === 'Enter') {
            e.preventDefault()
            const value = e.target.value.trim()
            if (value) {
              handleCreate(value, { row })
            }
          }
        }
      })
    },
  },
  {
    title: '操作',
    key: 'actions',
    render(row, index) {
      return h(
        NButton,
        {
          type: 'error',
          onClick: () => vioTableACES.value.splice(index, 1),
        },
        { default: () => '删除' }
      )
    },
  },
])

// ACES表格列配置
const partColumnsACES = ref([
  {
    title: '目标库字段',
    key: 'goal',
    render(row) {
        return h(NSelect, {
        options: partselectOptionsACES.value,
        value: row.goalselectVal,
        filterable: true,
        clearable: true,
        consistentMenuWidth: false,
        // 优化虚拟滚动
        virtualScroll: true,
        // 添加样式穿透
        themeOverrides: {
          menuPadding: '4px'
        },
        onUpdateValue(v) {
          row.goalVal = v
        },
        onKeydown: (e) => {
          if (e.key === 'Enter') {
            e.preventDefault()
            const value = e.target.value.trim()
            if (value) {
              handleCreate(value, { row })
            }
          }
        }
      })
    },
  },
  {
  title: '源数据字段',
  key: 'source',
  render(row) {
    return h(NSelect, {
      options: sourceselectOptions.value,
      value: row.sourceselectVal,
      filterable: true,
      clearable: true,
      consistentMenuWidth: false,
      // 优化虚拟滚动
      virtualScroll: true,
      // 添加样式穿透
      themeOverrides: {
        menuPadding: '4px'
      },
      onUpdateValue(v) {
        row.selectVal = v
      },
      onKeydown: (e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          const value = e.target.value.trim()
          if (value) {
            handleCreate(value, { row })
          }
        }
      }
    })
  }
  },
  {
    title: '操作',
    key: 'actions',
    render(row, index) {
      return h(
        NButton,
        {
          type: 'error',
          onClick: () => partTableACES.value.splice(index, 1),
        },
        { default: () => '删除' }
      )
    },
  },
])

const sourceColumns = ref([
  {
    title: '目标库字段',
    key: 'goal',
    render(row) {
      return h(NInput, {
        value: row.source,
        readonly: false,
        onUpdateValue(v) {
          row.source = v
        },
      })
    },
  },
  {
  title: '源数据字段',
  key: 'source',
  render(row) {
    return h(NSelect, {
      options: sourceselectOptions.value,
      value: row.selectVal,
      filterable: true,
      clearable: true,
      consistentMenuWidth: false,
      // 优化虚拟滚动
      virtualScroll: true,
      showUnmatched: true,
      // 添加样式穿透
      themeOverrides: {
        menuPadding: '4px'
      },
      onUpdateValue(v) {
        row.selectVal = v
      },
      onKeydown: (e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          const value = e.target.value.trim()
          if (value) {
            handleCreate(value, { row })
          }
        }
      }
    })
  }
  },
  {
    title: '操作',
    key: 'actions',
    render(row, index) {
      return h(
        NButton,
        {
          type: 'error',
          onClick: () => sourceTable.value.splice(index, 1),
        },
        { default: () => '删除' }
      )
    },
  },
])

const partColumnsLY = ref([
{
    title: '目标库字段',
    key: 'goal',
    render(row) {
        return h(NSelect, {
        options: partselectOptionsACES.value,
        value: row.goalselectVal,
        filterable: true,
        clearable: true,
        consistentMenuWidth: false,
        // 优化虚拟滚动
        virtualScroll: true,
        // 添加样式穿透
        themeOverrides: {
          menuPadding: '4px'
        },
        onUpdateValue(v) {
          row.goalVal = v
        },
        onKeydown: (e) => {
          if (e.key === 'Enter') {
            e.preventDefault()
            const value = e.target.value.trim()
            if (value) {
              handleCreate(value, { row })
            }
          }
        }
      })
    },
  },
  {
  title: '源数据字段',
  key: 'source',
  render(row) {
    return h(NSelect, {
      options: sourceselectOptionsLY.value,
      value: row.selectVal,
      filterable: true,
      clearable: true,
      consistentMenuWidth: false,
      // 优化虚拟滚动
      virtualScroll: true,
      // 添加样式穿透
      themeOverrides: {
        menuPadding: '4px'
      },
      onUpdateValue(v) {
        row.selectVal = v
      },
      onKeydown: (e) => {
        if (e.key === 'Enter') {
          e.preventDefault()
          const value = e.target.value.trim()
          if (value) {
            handleCreate(value, { row })
          }
        }
      }
    })
  }
  },
  {
    title: '操作',
    key: 'actions',
    render(row, index) {
      return h(
        NButton,
        {
          type: 'error',
          onClick: () => partTableLY.value.splice(index, 1),
        },
        { default: () => '删除' }
      )
    },
  },
])

const vioChangeACES = async (value) => {
  vioValueACES.value = value
  if (!value) {
    vioselectOptionsACES.value = []
    return
  }
  try {
    const {data} = await api.getTableFieldsList({database:'BeforeProcessing',table_name: value })
    // 正确初始化表格数据
    // 确保返回数据是数组格式
    if (Array.isArray(data)) {
      vioselectOptionsACES.value = data.map(item => ({
        label: item,
        value: item,
        // 保持原有自定义逻辑
        isCustom: item.startsWith('custom_')
      }))
    }
  } catch (error) {
    console.error('获取字段失败:', error)
    //goalTable.value = []
  }
}

const partChange = async (value) => {
  partValue.value = value
  if (!value) {
    sourceselectOptionsLY.value = []
    return
  }
  try {
    
    const {data} = await api.getTableFieldsList({database:'BeforeProcessing',table_name: value })
    // 正确初始化表格数据
    // 确保返回数据是数组格式
    if (Array.isArray(data)) {
      sourceselectOptionsLY.value = data.map(item => ({
        label: item,
        value: item,
        // 保持原有自定义逻辑
        isCustom: item.startsWith('custom_')
      }))
    }
  } catch (error) {
    console.error('获取字段失败:', error)
  }
}

const sourceChange = async (value) => {
  sourceValue.value = value
  if (!value) {
    sourceselectOptions.value = []
    return
  }
  try {
    const {data} = await api.getTableFieldsList({database:'BeforeProcessing',table_name: value })
    const part = await api.getTableFieldsList({table_name: 'part_num'})
    // 正确初始化表格数据
    // 确保返回数据是数组格式
    if (Array.isArray(data)) {
      sourceselectOptions.value = data.map(item => ({
        label: item,
        value: item,
        // 保持原有自定义逻辑
        isCustom: item.startsWith('custom_')
      }))
    }

    if (Array.isArray(part.data)) {
      partselectOptionsACES.value = part.data.map(item => ({
        label: item,
        value: item,
        // 保持原有自定义逻辑
        isCustom: item.startsWith('custom_')
      }))
    }
  } catch (error) {
    console.error('获取字段失败:', error)
    //goalTable.value = []
  }
}

//新增行
const addSource = () => {
  sourceTable.value.push({
    source: '',
    selectVal: null,
    isCustom: 1,
  })
}

const addPartACES = () => {
  partTableACES.value.push({
    goalVal: null,
    selectVal: null,
    isCustom: 1,
  })
}

const addPartLY = () => {
  partTableLY.value.push({
    goalVal: null,
    selectVal: null,
    isCustom: 1,
  })
}

const addvioACES = () => {
  vioTableACES.value.push({
    vioVal: null,
    selectVal: null,
    isCustom: 1,
  })
}

const Save = () => {
  console.log(sourceValue.value)
  console.log(vioValueACES.value)
  console.log(reactiveToJson(sourceTable.value))
  console.log(reactiveToJson(partTableACES.value))
  console.log(reactiveToJson(vioTableACES.value))
}


function reactiveToJson(data, space = 2) {
  return JSON.parse(
    JSON.stringify(
      toRaw(data),
      (k, v) => v ?? null // 处理 undefined 值
    ),
    space
  )
}


// 处理卡片点击
const handleCardClick = (key) => {
  currentKey.value = key
  showModal.value = true
  loadDetailData(key)
}

// 关闭弹层时清空数据
const handleClose = () => {
  showModal.value = false
  sourceValue.value = []
  partValue.value = []
  vioValueACES.value = []
  sourceTable.value = []
  partTableACES.value = []
  vioTableACES.value = []
  sourceselectOptions.value = []
  partselectOptionsACES.value = []
  vioselectOptionsACES.value = []
}

// 加载详细数据（示例）
const loadDetailData = async (key) => {
  try {
      const g_response = await api.getTableFieldsList({table_name: "vehicle_data" })
      // 正确初始化表格数据
      sourceTable.value = g_response.data.map((item) => ({
        source: item,
        selectVal: null,
        isCustom: 0,
      }))

      const response = await api.getTableList({database:'BeforeProcessing'})
      sourceOptions.value = response.data.map((item) => ({
        label: item,
        value: item,
      }))

      if (key === 'ACES') {
        vioOptionsACES.value = response.data.map((item) => ({
          label: item, // 显示在下拉框的名称
          value: item, // 作为选择值的id
        }))
      }

      /* if(key === 'LY'){
        partOptions.value = response.data.map((item) => ({
          label: item, // 显示在下拉框的名称
          value: item, // 作为选择值的id
        }))
      } */
      // 其他卡片类型的处理...
    } catch (error) {    
      console.error('数据加载失败', error)
    } finally {
      loadingModels.value = false
    }
  }

// 初始化加载数据
onMounted(async () => {
  try {
    const res = await api.getSourceInfo()
    cardData.value = res.data
  } catch (error) {
    console.error('初始化数据加载失败', error)
  }finally {
    isLoading.value = false
  }
})

const handleCreate = (value, { row }) => {
  // 检查是否已存在该选项
  const exists = sourceselectOptions.value.some(opt => opt.value === value);
  if (!exists) {
    sourceselectOptions.value.push({
      label: value,
      value: value
    });
  }
  row.selectVal = value;
};

</script>

<style scoped>
/* 添加加载容器样式 */
.n-spin-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: auto;
}

.card .n-card:hover {
  transform: translateY(-4px);
  transition: transform 0.2s ease;
}
.n-statistic__label {
  font-size: 14px;
  color: #666;
}
.n-statistic-value {
  font-size: 18px;
  font-weight: bold;
}

/* 表格容器 */
.modal-container {
  height: 100%;
  padding: 8px;
}

.table-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.n-data-table) {
  flex: 1;
  --n-th-padding: 12px 16px;
  --n-td-padding: 10px 16px;
}

:deep(.n-data-table-base-table) {
  min-width: 100% !important;
}

:deep(.n-data-table-thead) {
  position: sticky;
  top: 0;
  background: white;
  z-index: 2;
  box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

</style>
