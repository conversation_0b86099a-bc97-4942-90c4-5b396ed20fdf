<template>
  <CommonPage>
    <div class="workbench-container">
      <!-- 顶部统计卡片 -->
      <div class="stats-header">
        <div class="stats-info">
          <span>VIO统计 6个</span>
          <span>OE转换率/报表 2357个</span>
          <span>车型与品配产品 4563个</span>
          <span>产品质量OE 345个</span>
          <span>产品质量OE 999个</span>
        </div>
        <div class="product-line-header">
          <h2>我的产品线</h2>
          <span class="my-settings">我的设置</span>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="main-content">
        <!-- 左侧内容 -->
        <div class="left-content">
          <!-- 产品线统计卡片 -->
          <div class="product-stats">
            <div v-for="item in productStats" :key="item.label" class="product-stat-card">
              <div class="stat-icon-wrapper" :style="{ backgroundColor: item.bgColor }">
                <n-icon :component="item.icon" size="24" color="white" />
              </div>
              <div class="stat-info">
                <div class="stat-label">{{ item.label }}</div>
                <div class="stat-value">{{ item.value }}</div>
              </div>
            </div>
          </div>

          <!-- 未读消息 -->
          <n-card class="message-card" :bordered="false">
            <template #header>
              <div class="card-header">
                <span>未读消息</span>
                <span class="unread-count">未读公告</span>
              </div>
            </template>
            <div class="message-list">
              <div v-for="(msg, index) in messages" :key="index" class="message-item">
                <div class="message-number">{{ index + 1 }}</div>
                <div class="message-content">
                  <div class="message-title">{{ msg.title }}</div>
                  <div class="message-status">{{ msg.status }}</div>
                </div>
                <div class="message-time">{{ msg.time }}</div>
                <n-icon class="message-arrow" :component="ChevronRight" size="16" />
              </div>
            </div>
          </n-card>

          <!-- 我的待办 -->
          <n-card class="todo-card" :bordered="false">
            <template #header>
              <span>我的待办</span>
            </template>
            <div class="todo-list">
              <div v-for="(todo, index) in todos" :key="index" class="todo-item">
                <n-checkbox v-model:checked="todo.completed" />
                <div class="todo-content">
                  <div class="todo-title">{{ todo.title }}</div>
                  <div class="todo-details">
                    <span>发起时间：{{ todo.startTime }}</span>
                    <span>执行人：{{ todo.executor }}</span>
                    <span>重要性：{{ todo.importance }}</span>
                  </div>
                  <div class="todo-details">
                    <span>创建时间：{{ todo.createTime }}</span>
                    <span>创建人：{{ todo.creator }}</span>
                  </div>
                </div>
                <n-button type="primary" size="small" class="todo-action">
                  {{ todo.actionText }}
                </n-button>
              </div>
            </div>
          </n-card>
        </div>

        <!-- 右侧内容 -->
        <div class="right-content">
          <!-- 快捷菜单 -->
          <n-card class="shortcut-card" :bordered="false">
            <template #header>
              <span>快捷菜单</span>
            </template>
            <div class="shortcut-grid">
              <div v-for="item in shortcuts" :key="item.label" class="shortcut-item">
                <div class="shortcut-icon">
                  <n-icon :component="item.icon" size="24" />
                </div>
                <span class="shortcut-label">{{ item.label }}</span>
              </div>
            </div>
          </n-card>

          <!-- 用户信息 -->
          <div class="user-info">
            <div class="user-avatar">
              <span class="avatar-text">{{ avatarText }}</span>
            </div>
            <div class="user-details">
              <div class="user-name">{{ userInfo.name }}</div>
              <div class="user-meta">
                <div>邮箱: {{ userInfo.email || '未设置' }}</div>
                <div class="user-id">用户ID: {{ userInfo.userId || '未知' }}</div>
                <div class="user-role">角色: {{ userRoleText }}</div>
                <div class="user-status">
                  状态: {{ userInfo.isActive ? '正常' : '已禁用' }}
                  {{ userInfo.isSuperUser ? ' (超级用户)' : '' }}
                </div>
              </div>
            </div>
          </div>

          <!-- 我的日程 -->
          <n-card class="calendar-card" :bordered="false">
            <template #header>
              <div class="calendar-header">
                <span>我的日程</span>
                <div class="calendar-nav">
                  <n-button text @click="prevMonth">
                    <n-icon :component="ChevronLeft" />
                  </n-button>
                  <span class="calendar-title">{{ currentMonthYear }}</span>
                  <n-button text @click="nextMonth">
                    <n-icon :component="ChevronRight" />
                  </n-button>
                </div>
              </div>
            </template>
            <div class="calendar-container">
              <div class="calendar-weekdays">
                <div v-for="day in weekdays" :key="day" class="weekday">{{ day }}</div>
              </div>
              <div class="calendar-days">
                <div
                  v-for="day in calendarDays"
                  :key="day.date"
                  class="calendar-day"
                  :class="{
                    'other-month': day.otherMonth,
                    today: day.isToday,
                    selected: day.isSelected,
                    'has-event': day.hasEvent,
                  }"
                  @click="selectDate(day)"
                >
                  <span class="day-number">{{ day.day }}</span>
                  <div v-if="day.hasEvent" class="event-dots">
                    <div v-for="i in day.eventCount" :key="i" class="event-dot"></div>
                  </div>
                </div>
              </div>
            </div>
          </n-card>
        </div>
      </div>
    </div>
  </CommonPage>
</template>

<script setup>
import { ref, computed } from 'vue'
import { NIcon, NCard, NButton, NCheckbox } from 'naive-ui'
import {
  FileText,
  Settings,
  ChartBar,
  ChevronRight,
  ChevronLeft,
  User,
  Database,
  Clipboard,
} from '@vicons/tabler'
import dayjs from 'dayjs'
import CommonPage from '@/components/page/CommonPage.vue'
import { useUserStore } from '@/store'

// 获取用户store
const userStore = useUserStore()

// 用户信息计算属性
const userInfo = computed(() => ({
  name: userStore.name || '未知用户',
  email: userStore.email || '',
  avatar: userStore.avatar || '',
  roles: userStore.role || [],
  isSuperUser: userStore.isSuperUser || false,
  isActive: userStore.isActive !== false,
  userId: userStore.userId || '',
}))

// 用户头像文字（取用户名第一个字符）
const avatarText = computed(() => {
  const name = userInfo.value.name
  return name ? name.charAt(0).toUpperCase() : 'U'
})

// 用户角色显示
const userRoleText = computed(() => {
  const roles = userInfo.value.roles
  if (roles && roles.length > 0) {
    return roles.map((role) => role.name || role).join('、')
  }
  return userInfo.value.isSuperUser ? '超级管理员' : '普通用户'
})

// 产品线统计数据
const productStats = ref([
  {
    label: '待审批',
    value: '27个',
    icon: Clipboard,
    bgColor: '#5B9BD5',
  },
  {
    label: 'OE转换率（澳洲）',
    value: '3247个',
    icon: ChartBar,
    bgColor: '#70AD47',
  },
  {
    label: 'VIO分析报告',
    value: '4个',
    icon: FileText,
    bgColor: '#FFC000',
  },
  {
    label: '源数据更新',
    value: '8个',
    icon: Database,
    bgColor: '#A5A5A5',
  },
  {
    label: '用户审批',
    value: '0个',
    icon: User,
    bgColor: '#FF6B6B',
  },
])

// 未读消息数据
const messages = ref([
  {
    title: '审批工单《新增物料》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
  {
    title: '审批工单《供应商变更》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
  {
    title: '审批工单《补充OE》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
  {
    title: '审批工单《纠正类型》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
  {
    title: '审批工单《补充NISSENS号》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
  {
    title: '审批工单《1.3L12345改为可匹配》',
    status: '待提交',
    time: '2024-07-09 13:36:00',
  },
])

// 待办事项数据
const todos = ref([
  {
    title: '非洲VIO统计',
    startTime: '10天前 17:00',
    executor: '张三、LJ',
    importance: '重要性：重要且紧急！！！',
    createTime: '10天前 16:00',
    creator: 'King',
    actionText: '完成待办',
    completed: false,
  },
  {
    title: '澳洲OE适配',
    startTime: '10天后 17:00',
    executor: '张三、LJ',
    importance: '重要性：重要且紧急！！！',
    createTime: '10天前 16:00',
    creator: 'King',
    actionText: '完成待办',
    completed: false,
  },
  {
    title: '审批流程规范',
    startTime: '10天后 17:00',
    executor: '张三、LJ',
    importance: '重要性：重要不紧急！',
    createTime: '10天前 16:00',
    creator: 'King',
    actionText: '完成待办',
    completed: false,
  },
])

// 快捷菜单数据
const shortcuts = ref([
  { label: '产品管理', icon: Database },
  { label: '导入数据源', icon: FileText },
  { label: '数据资料', icon: Clipboard },
  { label: '新增资料', icon: Settings },
  { label: '产品管理', icon: Database },
  { label: '导入数据源', icon: FileText },
  { label: '数据资料', icon: Clipboard },
  { label: '新增资料', icon: Settings },
])

// 日历相关数据
const currentDate = ref(dayjs())
const selectedDate = ref(dayjs())
const weekdays = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']

const currentMonthYear = computed(() => {
  return currentDate.value.format('YYYY年M月')
})

const calendarDays = computed(() => {
  const startOfMonth = currentDate.value.startOf('month')
  const endOfMonth = currentDate.value.endOf('month')
  const startOfWeek = startOfMonth.startOf('week').add(1, 'day') // 周一开始
  const endOfWeek = endOfMonth.endOf('week').add(1, 'day')

  const days = []
  let current = startOfWeek

  while (current.isBefore(endOfWeek) || current.isSame(endOfWeek, 'day')) {
    const isCurrentMonth = current.isSame(currentDate.value, 'month')
    const isToday = current.isSame(dayjs(), 'day')
    const isSelected = current.isSame(selectedDate.value, 'day')

    // 模拟一些日程事件
    const hasEvent = [15, 16, 18, 23, 30].includes(current.date()) && isCurrentMonth
    const eventCount = hasEvent ? Math.floor(Math.random() * 3) + 1 : 0

    days.push({
      date: current.format('YYYY-MM-DD'),
      day: current.date(),
      otherMonth: !isCurrentMonth,
      isToday,
      isSelected,
      hasEvent,
      eventCount,
    })

    current = current.add(1, 'day')
  }

  return days
})

const prevMonth = () => {
  currentDate.value = currentDate.value.subtract(1, 'month')
}

const nextMonth = () => {
  currentDate.value = currentDate.value.add(1, 'month')
}

const selectDate = (day) => {
  if (!day.otherMonth) {
    selectedDate.value = dayjs(day.date)
  }
}
</script>

<style scoped>
.workbench-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.stats-header {
  margin-bottom: 20px;
}

.stats-info {
  display: flex;
  gap: 20px;
  margin-bottom: 10px;
  font-size: 14px;
  color: #666;
}

.product-line-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.product-line-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
}

.my-settings {
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
}

.main-content {
  display: flex;
  gap: 20px;
}

.left-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-content {
  width: 320px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-stats {
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
}

.product-stat-card {
  flex: 1;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  padding: 20px;
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 80px;
}

.product-stat-card:nth-child(1) {
  background: linear-gradient(135deg, #5b9bd5 0%, #4a90e2 100%);
}

.product-stat-card:nth-child(2) {
  background: linear-gradient(135deg, #70ad47 0%, #5a9a3a 100%);
}

.product-stat-card:nth-child(3) {
  background: linear-gradient(135deg, #ffc000 0%, #e6ac00 100%);
}

.product-stat-card:nth-child(4) {
  background: linear-gradient(135deg, #a5a5a5 0%, #909090 100%);
}

.product-stat-card:nth-child(5) {
  background: linear-gradient(135deg, #ff6b6b 0%, #e55555 100%);
}

.stat-icon-wrapper {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.message-card,
.todo-card,
.shortcut-card,
.calendar-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.unread-count {
  color: #1890ff;
  font-size: 12px;
}

.message-list {
  max-height: 300px;
  overflow-y: auto;
}

.message-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
  gap: 12px;
}

.message-item:last-child {
  border-bottom: none;
}

.message-number {
  width: 20px;
  height: 20px;
  background: #1890ff;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  flex-shrink: 0;
}

.message-content {
  flex: 1;
}

.message-title {
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
}

.message-status {
  font-size: 12px;
  color: #666;
}

.message-time {
  font-size: 12px;
  color: #999;
  white-space: nowrap;
}

.message-arrow {
  color: #ccc;
}

.todo-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.todo-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.todo-content {
  flex: 1;
}

.todo-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.todo-details {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.todo-action {
  flex-shrink: 0;
}

.shortcut-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

.shortcut-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.shortcut-item:hover {
  background-color: #f5f5f5;
}

.shortcut-icon {
  width: 32px;
  height: 32px;
  background: #1890ff;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  margin-bottom: 8px;
}

.shortcut-label {
  font-size: 12px;
  color: #333;
  text-align: center;
}

.user-info {
  background: white;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 12px;
}

.user-avatar {
  width: 48px;
  height: 48px;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  flex-shrink: 0;
}

.avatar-text {
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.user-meta {
  font-size: 12px;
  color: #666;
  line-height: 1.5;
}

.user-id,
.user-account,
.user-role,
.user-status {
  margin-top: 2px;
}

.user-status {
  color: #52c41a;
  font-weight: 500;
}

.calendar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.calendar-nav {
  display: flex;
  align-items: center;
  gap: 8px;
}

.calendar-title {
  font-size: 14px;
  font-weight: 500;
  min-width: 80px;
  text-align: center;
}

.calendar-container {
  padding: 16px 0;
}

.calendar-weekdays {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
  margin-bottom: 8px;
}

.weekday {
  text-align: center;
  font-size: 12px;
  color: #666;
  padding: 8px 4px;
}

.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 1px;
}

.calendar-day {
  aspect-ratio: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 4px;
  position: relative;
  transition: background-color 0.2s;
}

.calendar-day:hover {
  background-color: #f0f0f0;
}

.calendar-day.other-month {
  color: #ccc;
}

.calendar-day.today {
  background-color: #1890ff;
  color: white;
}

.calendar-day.selected {
  background-color: #1890ff;
  color: white;
}

.day-number {
  font-size: 12px;
  margin-bottom: 2px;
}

.event-dots {
  display: flex;
  gap: 2px;
  position: absolute;
  bottom: 2px;
}

.event-dot {
  width: 4px;
  height: 4px;
  background-color: #1890ff;
  border-radius: 50%;
}

.calendar-day.today .event-dot,
.calendar-day.selected .event-dot {
  background-color: white;
}

.calendar-day.has-event .day-number {
  font-weight: bold;
}
</style>
