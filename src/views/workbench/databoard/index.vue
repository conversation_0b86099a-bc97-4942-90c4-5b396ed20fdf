<template>
  <CommonPage>
    <div class="dashboard-root">
      <!-- 整合顶部统计卡片、地图和右侧柱状图 -->
      <div class="dashboard-main-container">
        <!-- 左侧区域（统计卡片+地图） -->
        <div class="dashboard-left-column">
          <!-- 顶部统计卡片 -->
          <div class="dashboard-header-row">
            <n-card class="dashboard-header-card" :bordered="false">
              <div class="dashboard-stats">
                <div v-for="item in stats" :key="item.label" class="stat-item">
                  <n-icon :component="item.icon" size="32" class="stat-icon" />
                  <div class="stat-label">{{ item.label }}</div>
                  <div class="stat-value">{{ item.value }}</div>
                </div>
              </div>
            </n-card>
          </div>

          <!-- 地图 -->
          <n-card class="dashboard-map-card" :bordered="false">
            <vue-echarts
              v-if="mapReady && mapOption"
              :option="mapOption"
              autoresize
              class="dashboard-map-echarts"
            />
          </n-card>
        </div>

        <!-- 右侧柱状图 -->
        <n-card class="dashboard-side-card" :bordered="false">
          <div class="dashboard-side-inner">
            <div class="dashboard-filter">
              <n-breadcrumb>
                <n-breadcrumb-item v-for="(item, idx) in filterPath" :key="idx">
                  {{ item }}
                </n-breadcrumb-item>
              </n-breadcrumb>
            </div>
            <div class="dashboard-bar-title">
              <span>{{ barTitle }}</span>
              <n-dropdown :options="dropdownOptions" trigger="click">
                <n-button text style="margin-left: 8px">
                  {{ barDropdownLabel }} <n-icon size="16" />
                </n-button>
              </n-dropdown>
            </div>
            <div class="dashboard-bar-percent">{{ barPercent }}</div>
            <div class="dashboard-bar-desc">截止今日</div>
            <div class="dashboard-bar-echarts-wrap">
              <vue-echarts
                v-if="barOption && barOption.series && barOption.series.length"
                :option="barOption"
                autoresize
                class="dashboard-bar-echarts"
              />
            </div>
          </div>
        </n-card>
      </div>

      <!-- 底部折线图 -->
      <n-card class="dashboard-line-card" :bordered="false">
        <div class="dashboard-line-title">新品开发迭代曲线</div>
        <vue-echarts
          v-if="lineOption && lineOption.series && lineOption.series.length"
          :option="lineOption"
          autoresize
          style="height: 220px; width: 100%"
        />
      </n-card>
    </div>
  </CommonPage>
</template>

<script setup>
import { ref, shallowRef, onMounted, nextTick } from 'vue'
import { NIcon, NCard, NBreadcrumb, NBreadcrumbItem, NDropdown, NButton } from 'naive-ui'
import { Apps, ChartPie, Box } from '@vicons/tabler'
import * as echarts from 'echarts/core'
import { use } from 'echarts/core'
import { MapChart, BarChart, LineChart } from 'echarts/charts'
import {
  TooltipComponent,
  VisualMapComponent,
  GridComponent,
  LegendComponent,
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

use([
  MapChart,
  BarChart,
  LineChart,
  TooltipComponent,
  VisualMapComponent,
  GridComponent,
  LegendComponent,
  CanvasRenderer,
])

const mapReady = ref(false)

onMounted(() => {
  fetch('/world_zh.json')
    .then((res) => res.json())
    .then((json) => {
      echarts.registerMap('world', json)
      nextTick(() => {
        mapReady.value = true
      })
    })
})

// 统计卡片数据（示例）
const stats = shallowRef([
  { label: '市场区域', value: '18个', icon: Apps },
  { label: '产品覆盖率', value: '94%', icon: ChartPie },
  { label: '产品品类', value: '12+', icon: Box },
  { label: 'SKU', value: '3200+', icon: Box },
])

const filterPath = ref(['EC', '水箱', '美国'])
const barTitle = ref('水箱-美国-覆盖率')
const barDropdownLabel = ref('小车/卡车')
const dropdownOptions = [
  { label: '小车/卡车', key: 'car' },
  { label: 'SUV', key: 'suv' },
]
const barPercent = ref('94%')

const barOption = ref({
  tooltip: { trigger: 'axis' },
  legend: {
    data: ['A类', 'B类'],
    top: 16,
    left: 'center',
    itemWidth: 12,
    itemHeight: 8,
    textStyle: { fontSize: 14 },
    icon: 'rect',
  },
  grid: { left: 24, right: 24, bottom: 32, top: 48 },
  xAxis: {
    type: 'category',
    data: ['一月', '二月', '三月', '四月', '五月', '六月'],
    axisTick: { show: false },
    axisLine: { lineStyle: { color: '#e5e7eb' } },
    axisLabel: { fontSize: 14 },
  },
  yAxis: {
    type: 'value',
    splitLine: { lineStyle: { color: '#f0f0f0' } },
    axisLabel: { fontSize: 14 },
  },
  series: [
    {
      name: 'A类',
      type: 'bar',
      data: [23, 45, 56, 33, 12, 34],
      barWidth: 8,
      itemStyle: {
        color: '#409eff',
        borderRadius: [6, 6, 0, 0],
      },
    },
    {
      name: 'B类',
      type: 'bar',
      data: [12, 34, 22, 44, 78, 45],
      barWidth: 8,
      itemStyle: {
        color: '#67c23a',
        borderRadius: [6, 6, 0, 0],
      },
    },
  ],
})

const lineOption = ref({
  tooltip: { trigger: 'axis' },
  legend: { data: ['2022', '2023'] },
  xAxis: {
    type: 'category',
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  },
  yAxis: { type: 'value' },
  series: [
    {
      name: '2022',
      type: 'line',
      data: [120, 132, 101, 134, 90, 230, 210, 180, 160, 140, 120, 100],
    },
    {
      name: '2023',
      type: 'line',
      data: [220, 182, 191, 234, 290, 330, 310, 300, 280, 260, 240, 220],
    },
  ],
})

const mapOption = ref({
  tooltip: {
    trigger: 'item',
    formatter: function (params) {
      const southeastAsia = ['泰国', '马来西亚', '菲律宾', '印度尼西亚']
      if (southeastAsia.includes(params.name)) {
        return '东南亚四国总市场分布: 20000'
      }
      return params.name + ': ' + (params.value || 0)
    },
  },
  // visualMap: {
  //   min: 0,
  //   max: 30000,
  //   left: 'left',
  //   top: 'bottom',
  //   text: ['高', '低'],
  // inRange: { color: ['#e0f3ff', '#409eff'] },
  //   calculable: false,
  // },
  series: [
    {
      name: '市场分布',
      type: 'map',
      map: 'world',
      roam: true,
      scaleLimit: {
        min: 1,
        max: 8,
      },
      itemStyle: {
        borderColor: '#f7f9fb',
        borderWidth: 1,
        areaColor: '#EFF0F0',
      },
      emphasis: {
        label: { show: true },
        itemStyle: {
          borderColor: '#409eff',
          borderWidth: 1.5,
          areaColor: '#b3e5fc',
        },
      },
      data: [
        { name: '美国', value: 27380, itemStyle: { areaColor: '#44B8CC' } },
        { name: '中国', value: 19800, itemStyle: { areaColor: '#3BC687' } },
        { name: '德国', value: 12000, itemStyle: { areaColor: '#708ACF' } },
        { name: '巴西', value: 8000, itemStyle: { areaColor: '#DBCF53' } },
        { name: '澳大利亚', value: 5000, itemStyle: { areaColor: '#B476D5' } },
        { name: '泰国', value: 0, itemStyle: { areaColor: '#009951' } },
        { name: '马来西亚', value: 0, itemStyle: { areaColor: '#009951' } },
        { name: '菲律宾', value: 0, itemStyle: { areaColor: '#009951' } },
        { name: '印度尼西亚', value: 0, itemStyle: { areaColor: '#009951' } },
      ],
    },
  ],
})
</script>

<style scoped>
.dashboard-root {
  background: #f7f9fb;
  min-height: 100vh;
  padding: 24px 0 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.dashboard-main-container {
  display: flex;
  gap: 24px;
  width: 1100px;
  max-width: 100vw;
  margin: 0 auto 24px;
  align-items: flex-start;
}

.dashboard-left-column {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.dashboard-header-row,
.dashboard-line-card {
  width: 100%;
  max-width: 100vw;
  margin: 0 auto;
}

.dashboard-header-card {
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  padding: 0 32px;
  box-sizing: border-box;
}

.dashboard-stats {
  display: flex;
  justify-content: space-between;
  gap: 48px;
  padding: 18px 0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 120px;
}

.stat-icon {
  margin-bottom: 6px;
  color: #409eff;
}

.stat-label {
  font-size: 15px;
  color: #888;
}

.stat-value {
  font-size: 22px;
  font-weight: bold;
  color: #222;
}

.dashboard-map-card {
  flex: 1;
  min-width: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  padding: 0;
  height: 400px;
  min-height: 400px; /* 添加最小高度 */
  display: flex;
}

.dashboard-map-echarts {
  width: 100% !important;
  height: 100% !important;
  min-height: 370px; /* 添加最小高度 */
}

.dashboard-side-card {
  width: 340px;
  flex-shrink: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
  padding: 0;
  height: 100%;
  min-height: 595px;
  box-sizing: border-box;
}

.dashboard-side-inner {
  display: flex;
  flex-direction: column;
  height: 100%;
  padding: 24px 24px 12px 24px;
}

.dashboard-bar-echarts-wrap {
  flex: 1;
  display: flex;
  align-items: flex-end;
  min-height: 0;
}

.dashboard-bar-echarts {
  width: 100% !important;
  height: 380px !important;
  min-height: 0;
}

.dashboard-filter {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.dashboard-bar-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
}

.dashboard-bar-percent {
  font-size: 32px;
  color: #409eff;
  font-weight: bold;
  margin-bottom: 2px;
}

.dashboard-bar-desc {
  font-size: 13px;
  color: #888;
  margin-bottom: 8px;
}

.dashboard-line-card {
  width: 1100px;
  margin-top: 0;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.03);
}

.dashboard-line-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 8px;
  padding: 24px 24px 0 24px;
}
</style>
